<div class="modal-body">
  <form [formGroup]="formClause" (ngSubmit)="onSubmit()" class="row">
    <div class="col-12">
      <div class="form-group">
        <label
          for="basicTextarea"
          class="w-100 align-items-center d-flex justify-content-between"
          >{{ title }}
          <div class="">
            <button
              class="btn btn-sm ml-auto p-0"
              (click)="modal.dismiss('Cross click')"
            >
              <img src="assets/images/icons/x.svg" alt="x" />
            </button></div
        ></label>
      </div>
    </div>
    <div class="col-2">
      <div class="form-group">
        <label for="documentName">Điều</label>
        <input
          [ngClass]="
            (f.clause_id.errors && f.clause_id?.touched) ||
            (submitted && f.clause_id?.errors?.required) ||
            isDuplicateClauseId
              ? 'is-invalid'
              : ''
          "
          type="number"
          class="form-control"
          formControlName="clause_id"
          placeholder="Điều"
        />
        <div class="col-sm-12 p-0" *ngIf="isDuplicateClauseId">
          <span class="invalid-form">
            <small class="form-text text-danger">{{ duplicateWarningMessage }}</small>
          </span>
        </div>
      </div>
    </div>
    <div class="col-3">
      <label for="documentName">Vị trí</label>
      <input
        [ngClass]="
          (f.position.errors && f.position?.touched) ||
          (submitted && f.position?.errors?.required)
            ? 'is-invalid'
            : ''
        "
        type="text"
        class="form-control"
        formControlName="position"
        placeholder="Vị trí"
      />
      <div class="col-sm-12 p-0">
        <span
          *ngIf="
            (submitted && f.position.errors) ||
            (f.position.errors && f.position?.touched)
          "
          class="invalid-form"
        >
          <small class="form-text text-danger"> Không được bỏ trống </small>
        </span>
      </div>
    </div>

    <div class="col-7">
      <div class="form-group">
        <label for="documentName">Tên điều khoản</label>
        <input
          [ngClass]="
            (f.title.errors && f.title?.touched) ||
            (submitted && f.title?.errors?.required)
              ? 'is-invalid'
              : ''
          "
          type="text"
          class="form-control"
          formControlName="title"
          placeholder="Tên điều khoản"
        />
        <div class="col-sm-12 p-0">
          <span
            *ngIf="
              (submitted && f.title.errors) ||
              (f.title.errors && f.title?.touched)
            "
            class="invalid-form"
          >
            <small class="form-text text-danger"> Không được bỏ trống </small>
          </span>
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group">
        <label for="documentName">Nội dung</label>
        <textarea
          [ngClass]="
            (f.raw_content.errors && f.raw_content?.touched) ||
            (submitted && f.raw_content?.errors?.required)
              ? 'is-invalid'
              : ''
          "
          type="text"
          row="7"
          class="form-control"
          formControlName="raw_content"
          placeholder="Nội dung"
        ></textarea>
        <div class="col-sm-12 p-0">
          <span
            *ngIf="
              (submitted && f.raw_content.errors) ||
              (f.raw_content.errors && f.raw_content?.touched)
            "
            class="invalid-form"
          >
            <small class="form-text text-danger"> Không được bỏ trống </small>
          </span>
        </div>
      </div>
    </div>
  </form>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      (click)="onSubmit()"
      [disabled]="formClause.invalid || isDuplicateClauseId"
    >
      Lưu
    </button>
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
      Hủy
    </button>
  </div>
</div>
