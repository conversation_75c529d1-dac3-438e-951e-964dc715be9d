// auth-redirect.guard.ts
import { Injectable } from "@angular/core";
import { CanActivate, Router } from "@angular/router";
import { Observable } from "rxjs";
import { Role } from "../models";
import { AuthenticationService } from "../service";

@Injectable({
  providedIn: "root",
})
export class AuthRedirectGuard implements CanActivate {
  constructor(
    private authService: AuthenticationService,
    private router: Router
  ) { }

  canActivate(): Observable<boolean> | Promise<boolean> | boolean {
    const currentUser = this.authService.currentUserValue;
    if (!currentUser) {
      this.router.navigate(["/pages/authentication/login-v2"]);
      return false;
    }
    const role = this.authService.currentUserValue?.role;

    if (role === Role.Super_admin || role === Role.Admin) {
      this.router.navigate(["/super-admin/dashboard"]);
    } else if (role === Role.User) {
      this.router.navigate(["/quan-ly-van-ban"]);
    } else {
      this.router.navigate(["/pages/miscellaneous/error"]);
    }

    return false; // Không cho truy cập route này, chỉ redirect
  }
}
