<!-- app-breadcrumb start -->
<div
  class="breadcrumb-wrapper w-100 d-flex"
  [ngClass]="{
    'justify-content-center': breadcrumb.alignment == 'center',
    'justify-content-end': breadcrumb.alignment == 'right'
  }"
>
  <ol
    class="breadcrumb breadcrumb-custom w-100"
    [ngClass]="{
      'breadcrumb-slash': breadcrumb.type == 'slash',
      'breadcrumb-dots': breadcrumb.type == 'dots',
      'breadcrumb-dashes': breadcrumb.type == 'dashes',
      'breadcrumb-pipes': breadcrumb.type == 'pipes',
      'breadcrumb-chevron': breadcrumb.type == 'chevron',
      'mr-1': breadcrumb.alignment == 'right'
    }"
  >
    <li
      class="breadcrumb-item breadcrumb-item-custom"
      *ngFor="let link of breadcrumb.links"
    >
      <!-- Nếu là header thì chỉ hiển thị header -->
      <ng-container *ngIf="link.isHeader; else notHeader">
        <h2 class="content-header-title float-left mb-0 font-medium-5 font-weight-bolder header-custom-font">
          {{ link.name }}
        </h2>
      </ng-container>

      <!-- Nếu không phải header thì kiểm tra isLink -->
      <ng-template #notHeader>
        <h2 class="d-inline">
          <a class="text-primary-theme" *ngIf="link.isLink" [routerLink]="link.link">{{ link.name }}</a>
        </h2>
        <span *ngIf="!link.isLink">{{ link.name }}</span>
      </ng-template>
    </li>
  </ol>
</div>
<!-- app-breadcrumb end -->
