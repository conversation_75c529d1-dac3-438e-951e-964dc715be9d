import { Component, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from "@angular/core";

import { Subject } from "rxjs";
import { filter, takeUntil } from "rxjs/operators";

import { NavigationEnd, Router } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { AuthenticationService } from "app/auth/service";

@Component({
  selector: "vertical-layout",
  templateUrl: "./vertical-layout.component.html",
  styleUrls: ["./vertical-layout.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class VerticalLayoutComponent implements OnInit, OnDestroy {
  coreConfig: any;
  public role: string = "";
  // Private
  private _unsubscribeAll: Subject<any>;
  public isUrlAdmin: boolean = false;
  public isCommercialRoute: boolean = false;
  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   */
  constructor(
    private _coreConfigService: CoreConfigService,
    private _authService: AuthenticationService,
    private router: Router
  ) {
    // Set the private defaults
    this._unsubscribeAll = new Subject();
    this.role = _authService.currentUserValue?.role;
    this.isCommercialRoute = this.router.url.startsWith("/commercial");

    this.router.events
      .pipe(filter((event): event is NavigationEnd => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        const url = event.urlAfterRedirects || event.url;

        if (url.includes("super-admin") || url.includes("cms") || url.includes("quan-ly-van-ban")) {
          this.isUrlAdmin = true;
        } else {
          this.isUrlAdmin = false;
        }

        // cập nhật mỗi lần đổi route
        this.isCommercialRoute = url.startsWith("/commercial");
      });
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    // Subscribe to config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
      });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }
}
