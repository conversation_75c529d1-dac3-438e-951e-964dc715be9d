import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { CoreConfigService } from "@core/services/config.service";
import {
  getLogo<PERSON>mage as getLogo<PERSON>mage<PERSON>elper,
  getA<PERSON><PERSON><PERSON> as getAppN<PERSON><PERSON>elper,
  isVP<PERSON><PERSON><PERSON><PERSON>,
  VPQH_COLOR_CODE,
} from "app/shared/image.helper";
import { BehaviorSubject, Observable, of } from "rxjs";
import { map, switchMap, catchError } from "rxjs/operators";

import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { User } from "app/auth/models";
import { environment } from "environments/environment";
import { ThemeService } from "@core/services/theme.service";
import { UserService } from "./user.service";
import { WebSocketService } from "./webSocket.service";
import { CmsService } from 'app/main/apps/cms/cms.service';
import { SocialAuthService } from "@abacritt/angularx-social-login";

@Injectable({ providedIn: "root" })
export class AuthenticationService {
  //public
  public currentUser: Observable<User>;

  //private
  public currentUserSubject: BehaviorSubject<User>;

  public package: Observable<any>;
  //private
  public token: Observable<string>;
  private packageSubject: BehaviorSubject<User>;
  private tokenSubject: BehaviorSubject<string>;

  constructor(
    private _http: HttpClient,
    private webSocketService: WebSocketService,
    private userService: UserService,
    private _coreConfigService: CoreConfigService,
    private _themeService: ThemeService,
    private cmsService: CmsService,
    private _socialAuthService: SocialAuthService,
  ) {
    this.currentUserSubject = new BehaviorSubject<User>(
      JSON.parse(localStorage.getItem("current_User"))
    );
    this.currentUser = this.currentUserSubject.asObservable();

    this.packageSubject = new BehaviorSubject<any>(
      JSON.parse(localStorage.getItem("package"))
    );
    this.package = this.packageSubject.asObservable();
    this.tokenSubject = new BehaviorSubject<string>(
      localStorage.getItem("token") != "undefined"
        ? localStorage.getItem("token")
        : null
    );
    this.token = this.tokenSubject.asObservable();
  }

  // getter: currentUserValue
  public get currentUserValue(): User {
    return this.currentUserSubject.value;
  }

  public get currentPackageValue(): any {
    return this.packageSubject.value;
  }

  public setTokenValue(value: string) {
    this.tokenSubject.next(value);
  }

  login(emailOrToken: string, password?: string) {
    let body: any;
    let url: string;
    if (password !== undefined) {
      body = { email: emailOrToken, password };
      url = `${environment.apiUrl}/auth/login`;
    } else {
      body = { token: emailOrToken };
      url = `${environment.apiUrl}/auth/google-login`;
    }
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http
      .post<any>(url, body, { headers })
      .pipe(
        switchMap((res) => {
          if (!(res?.user && res?.access)) return of(res);

          localStorage.setItem('token', res.access);
          this.webSocketService.reconnect();
          localStorage.setItem('refresh', res.refresh);
          localStorage.setItem('current_User', JSON.stringify(res.user));
          localStorage.setItem('isNewUser', JSON.stringify(res.isNewUser));
          localStorage.setItem('isADUser', JSON.stringify(res.isADUser));
          localStorage.setItem('workspace_id', res.workspace_id);
          localStorage.setItem('organization_id', res.organization_id);
          localStorage.setItem('background_image', res.background_image);
          localStorage.setItem('logo_image', res.logo_image);
          localStorage.setItem('color_code', res.color_code);
          localStorage.setItem('app_name', res.app_name);

          if (res.color_code) this._themeService.setTheme(res.color_code);
          this._coreConfigService.setConfig({
            app: {
              appLogoImage: getLogoImageHelper(),
              appName: getAppNameHelper(),
              appTitle: getAppNameHelper(),
            },
          }, { emitEvent: true });

          this.currentUserSubject.next(res.user);

          this.cmsService.resetCmsRoles();
          return this.cmsService.loadMyCmsRoles({ silence: false, force: true }).pipe(
            catchError(() => of([])),
            switchMap(() =>
              this.userService.getPackageByUserId(res.user.id).pipe(
                map((packageData) => {
                  if (packageData) localStorage.setItem('package', JSON.stringify(packageData));
                  return res;
                })
              )
            )
          );
        })
      );
  }

  getPackage() {
    // Gọi API lấy package của user
    return this.userService.getPackageByUserId(
      this.currentUserSubject.value.id
    );
  }

  async setPackage() {
    await this.getPackage()
      .toPromise()
      .then((res) => {
        if (res) {
          localStorage.setItem("package", JSON.stringify(res)); // Lưu package vào localStorage
        }
      });
  }

  /**
   * User logout
   *
   */
  logout() {
    // remove user from local storage to log user out
    const keysToRemove = [
      "token",
      "refresh",
      "current_User",
      "isNewUser",
      "workspace_id",
      "organization_id",
      "package",
      "filter",
      "cms_roles",
    ];

    // localStorage.clear();
    keysToRemove.forEach((k) => localStorage.removeItem(k));

    this.webSocketService.disconnect();
    // notify
    this.currentUserSubject.next(null);
    this.cmsService.resetCmsRoles();

    if (this._socialAuthService.authState && typeof this._socialAuthService.signOut === 'function') {
      this._socialAuthService.authState.subscribe(user => {
        if (user) {
          this._socialAuthService.signOut();
        }
      }).unsubscribe();
    }

    try { window.dispatchEvent(new CustomEvent('cms_roles_updated')); } catch { }
  }

  changePass(current_password: string, new_password: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.put<any>(`${environment.apiUrl}/auth/change-password`, {
      current_password,
      new_password,
    }, { headers });
  }

  addAccount(email: string, fullname: string, password: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post<any>(`${environment.apiUrl}/auth/create-user`, {
      email,
      fullname,
      password,
    }, { headers });
  }

  forgotPassword(email: string) {
    const formData = new FormData();
    formData.append("email", email);
    return this._http.post(
      `${environment.apiUrl}/auth/password-reset/`,
      formData
    );
  }

  refreshToken(refresh: string) {
    const formData = new FormData();
    const headers = new HttpHeaders()
      .set(InterceptorSkipHeader, "") // Custom header để skip Interceptor
      .set("Accept", "application/json; indent=4"); // Header Accept JSON
    formData.append("refresh", refresh);
    return this._http.post<any>(
      `${environment.apiUrl}/refresh-token/`,
      formData,
      {
        headers: headers,
        withCredentials: true,
      }
    );
  }

  report(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post<any>(`${environment.apiUrl}/report/`, body, { headers });
  }

  refreshAvatar() {
    const token = localStorage.getItem("token");
    const headers = new HttpHeaders()
      .set(InterceptorSkipHeader, "") // Custom header để skip Interceptor
      .set("Authorization", `Bearer ${token}`); // Thêm token vào header
    return this._http.get<any>(`${environment.apiUrl}/auth/refresh-avatar`, {
      headers,
    });
  }
}
